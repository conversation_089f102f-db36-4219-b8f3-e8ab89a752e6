package helper

import "gorm.io/gorm"

func normalPriceFeaturedNoDesign(tx *gorm.DB) (*gorm.DB, error) {
	query := tx.Table("data_database").
		Select([]string{
			"data_database.id",
			"data_database.receiving_Status",
			"data_database.cawangan",
			"data_database.dimension_Panjang",
			"data_database.dimension_Lebar",
			"data_database.dimension_Saiz",
			"data_database.kod_Purity",
			"data_database.kategori_Produk",
			"data_database.no_siri_Produk",
			"data_database.Berat",
			"data_database.Beza_Berat",
			"data_database.img_path",
			"data_database.img_path_2",
			"data_database.is_featured",
			"data_database.is_exclusive",
			"data_database.upah_g_comm_normal AS workmanship",
			"data_database.nota_lain_lain",
			"data_database.design",
			"data_database.code_design",
			"data_database.StatusItem",
			"data_database.is_sale",
			"data_database.diskaun_pelanggan_biasa AS discount",
			"data_database.kadar_diskaun_pelanggan_biasa AS promosi",
			"data_database.kategori_produk_ID",
			"hargaemas.Harga_Pelanggan AS gold_price",
		}).
		Joins("JOIN hargaemas ON data_database.kod_Purity = hargaemas.Purity").
		Where("data_database.StatusItem IN ?", []int{10, 51}).
		Where("data_database.combo_id IS NULL").
		Where("data_database.is_featured = ?", 1).
		Where("data_database.img_path IS NOT NULL").
		Where("data_database.receiving_Status IS NOT NULL").
		Where("hargaemas.cawangan = ?", "Online").
		SelectRaw(`
			CASE
				WHEN data_database.receiving_Status IN ('1','3','7','10') THEN data_database.code_Supplier - data_database.diskaun_pelanggan_biasa
				WHEN data_database.receiving_Status IN ('0','2','4','5','6','9') THEN data_database.Beza_Berat * (hargaemas.Harga_Pelanggan - data_database.diskaun_pelanggan_biasa)
			END AS price`).
		SelectRaw(`
			CASE
				WHEN data_database.receiving_Status IN ('1','3','7','10') THEN data_database.code_Supplier
				WHEN data_database.receiving_Status IN ('0','2','4','5','6','9') THEN data_database.Beza_Berat * hargaemas.Harga_Pelanggan
			END AS old_price`)

	// Optional branch filtering
	if settingProductBranch() == 1 {
		query = query.Where("data_database.cawangan = ?", "Online")
	}

	return query, nil
}