package api

import (
	"log"
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// Input validation struct
type FeaturedRequest struct {
	UserCategoryID int `json:"user_category_id" form:"user_category_id" query:"user_category_id" validate:"required"`
}

// Move NormalPriceFeaturedNoDesign here for now
func NormalPriceFeaturedNoDesign(tx *gorm.DB) (*gorm.DB, error) {
	query := tx.Table("data_database").
		Select([]string{
			"data_database.id",
			"data_database.receiving_Status",
			"data_database.cawangan",
			"data_database.dimension_Panjang",
			"data_database.dimension_Lebar",
			"data_database.dimension_Saiz",
			"data_database.kod_Purity",
			"data_database.kategori_Produk",
			"data_database.no_siri_Produk",
			"data_database.Berat",
			"data_database.Beza_Berat",
			"data_database.img_path",
			"data_database.img_path_2",
			"data_database.is_featured",
			"data_database.is_exclusive",
			"data_database.upah_g_comm_normal AS workmanship",
			"data_database.nota_lain_lain",
			"data_database.design",
			"data_database.code_design",
			"data_database.StatusItem",
			"data_database.is_sale",
			"data_database.diskaun_pelanggan_biasa AS discount",
			"data_database.kadar_diskaun_pelanggan_biasa AS promosi",
			"data_database.kategori_produk_ID",
			"hargaemas.Harga_Pelanggan AS gold_price",
			`CASE
				WHEN data_database.receiving_Status IN ('1','3','7','10') THEN data_database.code_Supplier - data_database.diskaun_pelanggan_biasa
				WHEN data_database.receiving_Status IN ('0','2','4','5','6','9') THEN data_database.Beza_Berat * (hargaemas.Harga_Pelanggan - data_database.diskaun_pelanggan_biasa)
			END AS price`,
			`CASE
				WHEN data_database.receiving_Status IN ('1','3','7','10') THEN data_database.code_Supplier
				WHEN data_database.receiving_Status IN ('0','2','4','5','6','9') THEN data_database.Beza_Berat * hargaemas.Harga_Pelanggan
			END AS old_price`,
		}).
		Joins("JOIN hargaemas ON data_database.kod_Purity = hargaemas.Purity").
		Where("data_database.StatusItem IN ?", []int{10, 51}).
		Where("data_database.combo_id IS NULL").
		Where("data_database.is_featured = ?", 1).
		Where("data_database.img_path IS NOT NULL").
		Where("data_database.receiving_Status IS NOT NULL").
		Where("hargaemas.cawangan = ?", "Online")
	return query, nil
}

// Featured returns featured products by user category
func FeaturedHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		var req FeaturedRequest
		if err := c.Bind(&req); err != nil {
			return c.JSON(http.StatusBadRequest, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusBadRequest,
				Error:      true,
				Message:    "Invalid request format",
				Data:       nil,
			})
		}
		if err := c.Validate(&req); err != nil {
			return c.JSON(http.StatusBadRequest, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusBadRequest,
				Error:      true,
				Message:    "Validation failed",
				Data:       nil,
			})
		}

		tx := db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		var products *gorm.DB
		var err error

		switch req.UserCategoryID {
		case 1:
			products, err = NormalPriceFeaturedNoDesign(tx)
		case 2:
			// TODO: Implement ahliPriceFeatured
			return c.JSON(http.StatusNotImplemented, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusNotImplemented,
				Error:      true,
				Message:    "ahliPriceFeatured not implemented",
				Data:       nil,
			})
		case 3:
			// TODO: Implement silverPriceFeatured
			return c.JSON(http.StatusNotImplemented, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusNotImplemented,
				Error:      true,
				Message:    "silverPriceFeatured not implemented",
				Data:       nil,
			})
		case 4:
			// TODO: Implement goldPriceFeatured
			return c.JSON(http.StatusNotImplemented, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusNotImplemented,
				Error:      true,
				Message:    "goldPriceFeatured not implemented",
				Data:       nil,
			})
		case 5:
			// TODO: Implement platiniumPriceFeatured
			return c.JSON(http.StatusNotImplemented, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusNotImplemented,
				Error:      true,
				Message:    "platiniumPriceFeatured not implemented",
				Data:       nil,
			})
		default:
			return c.JSON(http.StatusBadRequest, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusBadRequest,
				Error:      true,
				Message:    "Invalid category ID",
				Data:       nil,
			})
		}

		if err != nil {
			tx.Rollback()
			log.Println("Controller: Product Detail\nFunction: Featured\nError:", err)
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    "Something went wrong",
				Data:       nil,
			})
		}

		var result []map[string]interface{} // Use generic map for now
		if err := products.Find(&result).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    "Failed to fetch products",
				Data:       nil,
			})
		}

		tx.Commit()
		return c.JSON(http.StatusOK, ApiResponse[any]{
			Status:     "Success",
			StatusCode: http.StatusOK,
			Error:      false,
			Message:    "",
			Data:       result,
		})
	}
}
