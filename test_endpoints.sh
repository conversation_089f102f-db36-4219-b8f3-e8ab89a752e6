#!/bin/bash

echo "=== Testing All API Endpoints ==="
echo ""

# Test 1: Featured Product Endpoint
echo "1. Testing Featured Product Endpoint (/v1/product/featured)"
echo "Request: POST with JSON"
response1=$(curl -s -X POST "http://localhost:3000/v1/product/featured" \
  -H "Content-Type: application/json" \
  -H "key: Sankyu123" \
  -H "secret: Sankyu123" \
  -d '{"user_category_id": "1"}')

if echo "$response1" | grep -q '"status":"Success"'; then
    echo "✅ PASS: Featured Product (JSON)"
else
    echo "❌ FAIL: Featured Product (JSON)"
    echo "Response: $response1"
fi

echo ""

# Test 2: Featured Product with Form Data
echo "2. Testing Featured Product Endpoint with Form Data"
response2=$(curl -s -X POST "http://localhost:3000/v1/product/featured" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "key: Sankyu123" \
  -H "secret: Sankyu123" \
  -d "user_category_id=1")

if echo "$response2" | grep -q '"status":"Success"'; then
    echo "✅ PASS: Featured Product (Form Data)"
else
    echo "❌ FAIL: Featured Product (Form Data)"
    echo "Response: $response2"
fi

echo ""

# Test 3: Category Details Endpoint
echo "3. Testing Category Details Endpoint (/v1/category/details)"
echo "Request: POST with JSON"
response3=$(curl -s -X POST "http://localhost:3000/v1/category/details" \
  -H "Content-Type: application/json" \
  -H "key: Sankyu123" \
  -H "secret: Sankyu123" \
  -d '{"category_id": "13", "user_category_id": "1"}')

if echo "$response3" | grep -q '"status":"Success"'; then
    echo "✅ PASS: Category Details (JSON)"
    # Check if category fields are populated
    if echo "$response3" | grep -q '"Kategori_Produk":"NECKLACE"'; then
        echo "✅ PASS: Category fields populated correctly"
    else
        echo "⚠️  WARNING: Category fields might be empty"
    fi
else
    echo "❌ FAIL: Category Details (JSON)"
    echo "Response: $response3"
fi

echo ""

# Test 4: Category Details with Form Data
echo "4. Testing Category Details Endpoint with Form Data"
response4=$(curl -s -X POST "http://localhost:3000/v1/category/details" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "key: Sankyu123" \
  -H "secret: Sankyu123" \
  -d "category_id=13&user_category_id=1")

if echo "$response4" | grep -q '"status":"Success"'; then
    echo "✅ PASS: Category Details (Form Data)"
else
    echo "❌ FAIL: Category Details (Form Data)"
    echo "Response: $response4"
fi

echo ""

# Test 5: Category No Design Endpoint
echo "5. Testing Category No Design Endpoint (/v1/product/category-no-design)"
echo "Request: POST with JSON"
response5=$(curl -s -X POST "http://localhost:3000/v1/product/category-no-design" \
  -H "Content-Type: application/json" \
  -H "key: Sankyu123" \
  -H "secret: Sankyu123" \
  -d '{"category_id": "13", "user_category_id": "1"}')

if echo "$response5" | grep -q '"status":"Success"'; then
    echo "✅ PASS: Category No Design (JSON)"
    # Check if category fields are populated
    if echo "$response5" | grep -q '"Kategori_Produk":"NECKLACE"'; then
        echo "✅ PASS: Category fields populated correctly"
    else
        echo "⚠️  WARNING: Category fields might be empty"
    fi
else
    echo "❌ FAIL: Category No Design (JSON)"
    echo "Response: $response5"
fi

echo ""

# Test 6: Category No Design with Form Data
echo "6. Testing Category No Design Endpoint with Form Data"
response6=$(curl -s -X POST "http://localhost:3000/v1/product/category-no-design" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "key: Sankyu123" \
  -H "secret: Sankyu123" \
  -d "category_id=13&user_category_id=1")

if echo "$response6" | grep -q '"status":"Success"'; then
    echo "✅ PASS: Category No Design (Form Data)"
else
    echo "❌ FAIL: Category No Design (Form Data)"
    echo "Response: $response6"
fi

echo ""

# Test 7: Test with invalid data to ensure error handling works
echo "7. Testing Error Handling with Invalid Data"
response7=$(curl -s -X POST "http://localhost:3000/v1/category/details" \
  -H "Content-Type: application/json" \
  -H "key: Sankyu123" \
  -H "secret: Sankyu123" \
  -d '{"category_id": "invalid", "user_category_id": "1"}')

if echo "$response7" | grep -q '"message":"Invalid category_id format"'; then
    echo "✅ PASS: Error handling works correctly"
else
    echo "❌ FAIL: Error handling not working"
    echo "Response: $response7"
fi

echo ""
echo "=== Test Summary ==="
echo "All endpoints have been tested with both JSON and form data."
echo "If you're still getting 'Invalid request format' errors, please:"
echo "1. Check which exact endpoint your frontend is calling"
echo "2. Check the request headers and payload being sent"
echo "3. Ensure the Go server is running on the correct port"
